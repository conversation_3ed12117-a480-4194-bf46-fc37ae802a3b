import React, {useState} from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  useWindowDimensions,
  ScrollView,
} from 'react-native';
import Logo from '../assets/images/smartfuel-logo.png';
import CustomInput from '../components/customInput';
import CustomButton from '../components/customButton';
import {useNavigation} from '@react-navigation/native';

const ResetPasswordScreen = () => {
  const [companyCode, setCompanyCode] = useState('')
  const [username, setUsername] = useState('');
  const {height} = useWindowDimensions();
  const navigation = useNavigation();
  const onSignInPressed = () => {
    navigation.navigate('SignIn');
  };
  const onSendPressed = () => {
    console.warn('reset Password');
  };

  return (
    <ScrollView>
      <View style={styles.root}>
        <Image
          source={Logo}
          style={[styles.logo, {height: height * 0.3}]}
          resizeMode="contain"
        />
        <CustomInput
          placeholder="Company Code"
          value={companyCode}
          setValue={setCompanyCode}
        />
        <CustomInput
          placeholder="New Password"
          value={username}
          setValue={setUsername}
          secureTextEntry={true}
        />

        <CustomButton text="Reset Password" onPress={onSendPressed} />
        <CustomButton
          text={'Back to Sign In'}
          type={'TERTIARY'}
          onPress={onSignInPressed}
        />
      </View>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  root: {alignItems: 'center', padding: 20},
  logo: {maxWidth: 270, Width: '70%', maxHeight: 100},
});
export default ResetPasswordScreen;
