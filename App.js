/**
 * React Native App - OPT Mobile Whitecard
 * https://github.com/facebook/react-native
 *
 * @format
 * @flow strict-local
 */

import React, {useContext} from 'react';
import {SafeAreaView, StyleSheet, View} from 'react-native';

import {Navigation} from './app/navigation';

import {AuthenticationContextProvider} from './app/controllers/AuthContext';
import {NavigationContainer} from '@react-navigation/native';

const App = () => {
  return (
    <AuthenticationContextProvider>
      <SafeAreaView style={styles.root}>
        <NavigationContainer>
          <Navigation />
        </NavigationContainer>
      </SafeAreaView>
    </AuthenticationContextProvider>
  );
};
const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: '#F9FBFC',
  },
});
export default App;
