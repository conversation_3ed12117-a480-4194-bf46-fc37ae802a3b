import React, {useContext} from 'react';
import {
  Button, FlatList,
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  useWindowDimensions,
  View,
} from 'react-native';
import CustomButton from '../components/customButton';
import {AuthenticationContext} from '../controllers/AuthContext';
import Logo from '../assets/images/smartfuel-logo.png';
import {Card, CardContentProps, Paragraph, Title} from 'react-native-paper';
import {CardTitle} from 'react-native-paper/src/components/Card/CardTitle';
import CardContent from 'react-native-paper/src/components/Card/CardContent';

function TransactionScreen({navigation}) {
  const {onLogout} = useContext(AuthenticationContext);
  const onTransactionPressed = () => {
    navigation.navigate('TransactionScreen');
  };
  const onGenerateQRPressed = () => {
    navigation.navigate('QRCode');
  };
  const {height} = useWindowDimensions();
  return (
    <SafeAreaView style={styles.background}>
      <Image
        source={Logo}
        style={[styles.logo, {height: height * 0.3}]}
        resizeMode="contain"
      />
      <ScrollView>
        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>
        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>

        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>

        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>

        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>

        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>

        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>

        <Card style={styles.input}>
          <CardContent>
            <Title style={styles.titles}>Transaction #: 008-45678-456 </Title>
            <Paragraph style={styles.cardText}>Date: 2022-05-10 15:00:05</Paragraph>
            <Paragraph style={styles.cardText}>Registration: YTF-014T</Paragraph>
            <Paragraph style={styles.cardText}>Amount: $100.00</Paragraph>
            <CustomButton text={"Send Receipt"}/>
          </CardContent>
        </Card>


      </ScrollView>
      <View style={styles.root}>
        <CustomButton
          text="Generate QR"
          onPress={onGenerateQRPressed}
          type="PRIMARY"
        />
        <CustomButton text="Sign Out" onPress={onLogout} type="PRIMARY" />
      </View>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  background: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  logo: {maxWidth: 270, Width: '70%', maxHeight: 100},
  input: {
    width: 300,
    alignItems: 'flex-start',
    justifyContent: 'center',
    margin: 5,
    borderWidth: 1,
    padding: 15,
    backgroundColor: '#FFFFFF',
  },
  root: {
    padding: 10,
    flexDirection: 'row',
    margin: 5,
    width: '80%',
  },
  buttons: {
    alignItems: 'center',
    padding: 20,
    paddingHorizontal: 100,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  titles:{fontSize:12, fontWeight: 'bold'},
  cardText:{fontSize:12,}
});
export default TransactionScreen;
