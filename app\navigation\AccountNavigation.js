import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';

import TransactionScreen from '../screens/transactionScreen';
import QrCodeScreen from '../screens/qrCodeScreen';
import WelcomeScreen from '../screens/welcomeScreen';
import GeneratedQRCodeScreen from '../screens/generatedQRCodeScreen';

const Stack = createNativeStackNavigator();

export const AccountNavigation = () => (
  <Stack.Navigator screenOptions={{headerShown: false}}>
    <Stack.Screen name="QRCode" component={QrCodeScreen} />
    <Stack.Screen name="GeneratedQRCode" component={GeneratedQRCodeScreen} />
    <Stack.Screen name="TransactionScreen" component={TransactionScreen} />
    <Stack.Screen name="SignOut" component={WelcomeScreen} />
  </Stack.Navigator>
);
