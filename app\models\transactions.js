export default class transaction {
  transactionId: number;
  transactionDate: Date;
  transactionCurrency: string;
  transactionType: string;
  transactionStatus: string;
  terminalId: number;
  accountId: number;
  accountNumber: number;
  storeName: string;
  fuelPoint: string;
  fuelGradeDescription: string;
  fuelVolume: number;
  fuelPrice: number;
  amountExGST: number;
  GSTAmount: number;
  amountInclGST: number;
  constructor(
    Id,
    trnDate,
    trnCurrency,
    trnType,
    trnStatus,
    termId,
    accId,
    accNumber,
    storeName,
    fuelPoint,
    fuelGradeDesc,
    fuelVol,
    fuelPrice,
    amtExGst,
    gstAmt,
    amtIncGst,
  ) {
    this.transactionId = Id;
    this.transactionDate = trnDate;
    this.transactionCurrency = trnCurrency;
    this.transactionType = trnType;
    this.transactionStatus = trnStatus;
    this.terminalId = termId;
    this.accountId = accId;
    this.accountNumber = accNumber;
    this.storeName = storeName;
    this.fuelPoint = fuelPoint;
    this.fuelGradeDescription = fuelGradeDesc;
    this.fuelVolume = fuelVol;
    this.fuelPrice = fuelPrice;
    this.amountExGST = amtExGst;
    this.GSTAmount = gstAmt;
    this.amountInclGST = amtIncGst;
  }
}
