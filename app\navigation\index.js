import React, {useContext} from 'react';
import {NavigationContainer} from '@react-navigation/native';

import {AuthNavigation} from './AuthNavigation';
import {AccountNavigation} from './AccountNavigation';
import {AuthenticationContext} from '../controllers/AuthContext';

export const Navigation = () => {
  const {isAuthenticated} = useContext(AuthenticationContext);

  return (
    <>
      {isAuthenticated ? <AccountNavigation /> : <AuthNavigation />}
    </>
  );
};
