import React, {useContext, useState} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';

import LoginScreen from '../screens/loginScreen';
import UserRegistrationScreen from '../screens/userRegistrationScreen';
import ForgotPasswordScreen from '../screens/forgotPasswordScreen';
import ResetPasswordScreen from '../screens/resetPasswordScreen';

const Stack = createNativeStackNavigator();

export const AuthNavigation = () => (
  <Stack.Navigator screenOptions={{headerShown: false}}>
    <Stack.Screen name="SignIn" component={LoginScreen} />
    <Stack.Screen name="Registration" component={UserRegistrationScreen} />
    <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
  </Stack.Navigator>
);
