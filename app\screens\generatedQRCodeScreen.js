import React, {useContext, useState} from 'react';
import {
  Button,
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  useWindowDimensions,
  View,
} from 'react-native';
import CustomQrCode from '../components/customQrCode';
import CustomButton from '../components/customButton';
import {AuthenticationContext} from '../controllers/AuthContext';
import CustomInput from '../components/customInput';
import Logo from '../assets/images/smartfuel-logo.png';

function GeneratedQRCodeScreen({navigation}) {
  const [vehicleRegistration] = React.useState();
  const [vehicleOdometer] = React.useState();
  const {onLogout} = useContext(AuthenticationContext);
  const onTransactionPressed = () => {
    navigation.navigate('TransactionScreen');
  };
  const onGenerateQRPressed = () => {
    navigation.navigate('QRCode');
  };

  const initialItemState = {
    name: '<PERSON>',
    expiryDate: '2023-12-31',
    manufacturer: 'Kakira Sugar Estate',
  };
  const [item, setItem] = useState(initialItemState);
  const [productQRRef, setProductQRRef] = useState();
  const {height} = useWindowDimensions();
  return (
    <SafeAreaView style={styles.background}>
      <>
        <View style={styles.root}>
          <Text style={styles.root}>Scan to Start Fueling</Text>
          <CustomQrCode
            value={JSON.stringify({
              name: item.name,
              expiry: item.expiryDate,
              manufacturer: item.manufacturer,
            })}
            getRef={c => setProductQRRef(c)}
          />
        </View>
      </>
      <View style={styles.buttons}>
        <CustomButton
          text="Transactions"
          onPress={onTransactionPressed}
          type="PRIMARY"
        />
        <CustomButton text="Sign Out" onPress={onLogout} type="PRIMARY" />
      </View>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  root: {alignItems: 'center', padding: 50},
  logo: {maxWidth: 270, Width: '70%', maxHeight: 100},
  buttons: {
    alignItems: 'center',
    padding: 20,
    paddingHorizontal: 100,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  background: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
});
export default GeneratedQRCodeScreen;
