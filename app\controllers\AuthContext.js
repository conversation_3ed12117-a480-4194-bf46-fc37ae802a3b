import react, {createContext, useEffect, useState} from 'react';
import {BASE_API_URL} from '../config';
import {loginRequest} from '../services/auth/authentication.service';

export const AuthenticationContext = createContext();
export const AuthenticationContextProvider = ({children}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState(null);
  const [error, setError] = useState(null);

  const onRegister = (email, password, repeatedPassword) => {
    console.log('here', {email}, {password}, {repeatedPassword});
    setIsLoading(true);
    if (password !== repeatedPassword) {
      setError('Error: Passwords do not match');
      return;
    }

    /* REPLACE THIS WITH A CALL TO Register Request
    firebase
        .auth()
        .createUserWithEmailAndPassword(email, password)
        .then((u) => {
          setUser(u);
          setIsLoading(false);
        })
        .catch((err) => {
          setIsLoading(false);
          setError(err.toString());
        });*/
  };
  const onLogin = (userName, password) => {
    setIsLoading(true);
    loginRequest(userName, password)
      .then(u => {
        console.log(u);
        setUser(u);
        setIsLoading(false);
      })
      .catch(err => {
        setIsLoading(false);
        setError(err.toString());
      });
  };
  const onLogout = () => {
    setUser(null);
  };
  return (
    <AuthenticationContext.Provider
      value={{
        isAuthenticated: !!user,
        isLoading,
        user,
        error,
        onLogin,
        onRegister,
        onLogout,
      }}>
      {children}
    </AuthenticationContext.Provider>
  );
};

/*
*   const authUser = async (username, password) => {
    return fetch(`${BASE_API_URL}/userAuthentication`, {
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({username, password}),
    }).then(response => {
      response.json().then(r => {
        console.log('inner', r.userSessionToken);
        setUser(r.userSessionToken);
      });
    });
  };
  *
 */
