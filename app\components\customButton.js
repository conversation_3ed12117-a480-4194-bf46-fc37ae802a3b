import React from 'react';
import {Text, StyleSheet, Pressable} from 'react-native';

const CustomButton = ({onPress, text, type = 'PRIMARY'}) => {
  return (
    <Pressable
      onPress={onPress}
      style={[styles.container, styles[`container_${type}`]]}>
      <Text style={[styles.text, styles[`text_${type}`]]}>{text}</Text>
    </Pressable>
  );
};
const styles = StyleSheet.create({
  container: {
    padding: 10,
    marginVertical: 15,
    marginHorizontal: 10,
    alignItems: 'center',
    borderRadius: 5,
    justifyContent: 'space-between',
  },
  container_PRIMARY: {backgroundColor: '#3B71F3'},
  container_SECONDARY: {borderColor: '#3B71F3', borderWidth: 10},
  container_TERTIARY: {},
  text: {
    fontWeight: 'bold',
    color: 'white',
  },
  text_SECONDARY: {color: '#3B71F3'},
  text_TERTIARY: {color: '#c3c3c3'},
});
export default CustomButton;
