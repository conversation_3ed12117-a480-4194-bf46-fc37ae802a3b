import React, {useContext, useState} from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  useWindowDimensions,
  ScrollView,
} from 'react-native';
import Logo from '../assets/images/smartfuel-logo.png';
import CustomInput from '../components/customInput';
import CustomButton from '../components/customButton';

import {AuthenticationContext} from '../controllers/AuthContext';

const LoginScreen = ({navigation}) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const {height} = useWindowDimensions();
  const {onLogin, isLoading, error} = useContext(AuthenticationContext);

  const onSignInPressed = () => {
    onLogin(username, password);
  };
  const onForgotPasswordPressed = () => {
    navigation.navigate('ForgotPassword');
  };
  const onSignUpPressed = () => {
    navigation.navigate('Registration');
  };

  return (
    <ScrollView>
      <View style={styles.root}>
        <Image
          source={Logo}
          style={[styles.logo, {height: height * 0.3}]}
          resizeMode="contain"
        />
        <CustomInput
          placeholder="Email Address"
          value={username}
          setValue={setUsername}
          onChangeText={text => setUsername(text)}
        />
        <CustomInput
          placeholder="Password"
          value={password}
          setValue={setPassword}
          secureTextEntry
          onChangeText={text => setPassword(text)}
        />

        <CustomButton text="Sign In" onPress={onSignInPressed} />
        <CustomButton
          text="Forgot Password?"
          onPress={onForgotPasswordPressed}
          type="TERTIARY"
        />

        <CustomButton
          text="Sign Up"
          onPress={onSignUpPressed}
          type="TERTIARY"
        />
      </View>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  root: {alignItems: 'center', padding: 20},
  logo: {maxWidth: 270, Width: '70%', maxHeight: 100},
});
export default LoginScreen;
