import React, {useState} from 'react';
import {
  Text,
  View,
  Image,
  StyleSheet,
  useWindowDimensions,
  ScrollView,
} from 'react-native';
import Logo from '../assets/images/smartfuel-logo.png';
import CustomInput from '../components/customInput';
import CustomButton from '../components/customButton';
import {useNavigation} from '@react-navigation/native';

const ForgotPasswordScreen = () => {
  const [username, setUsername] = useState('');
  const {height} = useWindowDimensions();
  const navigation = useNavigation();
  const onSignInPressed = () => {
    navigation.navigate('SignIn');
  };
  const onSendPressed = () => {
    navigation.navigate('ResetPassword');
  };
  return (
    <ScrollView>
      <View style={styles.root}>
        <Image
          source={Logo}
          style={[styles.logo, {height: height * 0.3}]}
          resizeMode="contain"
        />
        <CustomInput
          placeholder="Email Address"
          value={username}
          setValue={setUsername}
        />

        <CustomButton text="Send" onPress={onSendPressed} />
        <CustomButton
          text={'Back to Sign In'}
          type={'TERTIARY'}
          onPress={onSignInPressed}
        />
      </View>
    </ScrollView>
  );
};
const styles = StyleSheet.create({
  root: {alignItems: 'center', padding: 20},
  logo: {maxWidth: 270, Width: '70%', maxHeight: 100},
});
export default ForgotPasswordScreen;
