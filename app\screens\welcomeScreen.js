import React from 'react';
import {Button, Image, SafeAreaView, StyleSheet, View} from 'react-native';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import LoginScreen from './loginScreen';
import UserRegistrationScreen from './userRegistrationScreen';

const Stack = createNativeStackNavigator();
const MyStack = () => {
  return (
    <Stack.Navigator>
      <Stack.Screen
        name="Home"
        component={WelcomeScreen}
        options={{title: 'Welcome'}}
      />
      <Stack.Screen name="Register" component={UserRegistrationScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
    </Stack.Navigator>
  );
};

function WelcomeScreen({navigation}) {
  return (
    <SafeAreaView style={styles.body}>
      <Image
        style={styles.logo}
        source={require('../assets/images/smartfuel-logo.png')}
      />
      <View style={styles.body} />
      <View style={styles.rowItem1}>
        <Button title="Login" onPress={() => navigation.navigate('Login')} />
        <Button
          title="Register"
          onPress={() => navigation.navigate('Register')}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  body: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: '#CCCCCC',
  },
  logo: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 100,
  },
  rowItem1: {
    width: '50%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
});
export default function App() {
  return (
    <NavigationContainer>
      <MyStack />
    </NavigationContainer>
  );
}
