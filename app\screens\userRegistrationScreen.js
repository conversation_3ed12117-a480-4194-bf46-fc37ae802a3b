import React, {useContext, useState} from 'react';
import {
  Button,
  Image,
  SafeAreaView,
  StyleSheet,
  Text,
  TextInput,
  View,
  ScrollView,
  useWindowDimensions,
} from 'react-native';
import Logo from '../assets/images/smartfuel-logo.png';
import CustomInput from '../components/customInput';
import CustomButton from '../components/customButton';
import {useNavigation} from '@react-navigation/native';
import {AuthenticationContext} from '../controllers/AuthContext';
const UserRegistrationScreen = () => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [emailAddress, setEmailAddress] = useState('');
  const [companyCode, setCompanyCode] = useState('');
  const [userPassword, setPassword] = useState('');
  const [confirmUserPassword, setConfirmPassword] = useState('');
  const {height} = useWindowDimensions();
  const {onRegister} = useContext(AuthenticationContext);
  const navigation = useNavigation();
  const onRegisterPressed = () => {
    console.log('Register Pressed');
    onRegister(firstName, emailAddress, companyCode);
  };
  const onSignInPressed = () => {
    navigation.navigate('SignIn');
  };
  const onTermsPressed = () => {
    console.warn('Terms Pressed');
  };
  const onPrivacyPressed = () => {
    console.warn('Privacy Pressed');
  };
  return (
    <SafeAreaView style={styles.background}>
      <ScrollView>
        <View style={styles.root}>
          <Image
            style={[styles.logo, {height: height * 0.3}]}
            source={Logo}
            resizeMode="contain"
          />
          <CustomInput
            style={styles.input}
            value={emailAddress}
            setValue={setEmailAddress}
            placeholder={'Email Address'}
          />
          <CustomInput
            style={styles.input}
            value={companyCode}
            setValue={setCompanyCode}
            placeholder={'Company Registration Code'}
          />
          <CustomInput
            secureTextEntry={true}
            style={styles.input}
            value={userPassword}
            setValue={setPassword}
            placeholder={'Password'}
          />
          <CustomInput
            secureTextEntry={true}
            style={styles.input}
            value={confirmUserPassword}
            setValue={setConfirmPassword}
            placeholder={'Confirm Password'}
          />
          <CustomButton text={'Register'} onPress={onRegisterPressed} />
          <Text style={styles.text}>
            By registering, you confirm that you accept our{' '}
            <Text style={styles.link} onPress={onTermsPressed}>
              Terms of Use
            </Text>{' '}
            and{' '}
            <Text style={styles.link} onPress={onPrivacyPressed}>
              Privacy Policy
            </Text>
          </Text>
          <CustomButton
            text={'Have an account - Sign In'}
            type={'TERTIARY'}
            onPress={onSignInPressed}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};
const styles = StyleSheet.create({
  root: {alignItems: 'center', padding: 20},
  logo: {maxWidth: 270, Width: '70%', maxHeight: 100},
  text: {color: 'gray', marginVertical: 10},
  link: {color: '#000099'},
});
export default UserRegistrationScreen;
